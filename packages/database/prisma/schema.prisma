
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

generator zod {
    provider         = "zod-prisma-types"
    output           = "./zod"
    createInputTypes = true
    addIncludeType   = false
    addSelectType    = false
}

generator json {
    provider = "prisma-json-types-generator"
}

model User {
    id                 String       @id @default(cuid())
    name               String
    email              String
    emailVerified      Boolean
    image              String?
    createdAt          DateTime
    updatedAt          DateTime
    username           String?
    role               String?
    banned             Boolean?
    banReason          String?
    banExpires         DateTime?
    onboardingComplete Boolean      @default(false)
    paymentsCustomerId String?
    locale             String?
    twoFactorEnabled   Boolean?
    sessions           Session[]
    accounts           Account[]
    passkeys           Passkey[]
    invitations        Invitation[]
    purchases          Purchase[]
    members            Member[]
    twofactors         TwoFactor[]
    aiChats            AiChat[]
    players            Player[]

    @@unique([email])
    @@unique([username])
    @@map("user")
}

model Session {
    id        String   @id @default(cuid())
    expiresAt DateTime
    ipAddress String?
    userAgent String?
    userId    String
    user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

    impersonatedBy String?

    activeOrganizationId String?

    token     String
    createdAt DateTime
    updatedAt DateTime

    @@unique([token])
    @@map("session")
}

model Account {
    id           String    @id @default(cuid())
    accountId    String
    providerId   String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    accessToken  String?   @db.Text
    refreshToken String?   @db.Text
    idToken      String?   @db.Text
    expiresAt    DateTime?
    password     String?

    accessTokenExpiresAt  DateTime?
    refreshTokenExpiresAt DateTime?
    scope                 String?
    createdAt             DateTime
    updatedAt             DateTime

    @@map("account")
}

model Verification {
    id         String   @id @default(cuid())
    identifier String
    value      String   @db.Text
    expiresAt  DateTime

    createdAt DateTime?
    updatedAt DateTime?

    @@map("verification")
}

model Passkey {
    id           String    @id @default(cuid())
    name         String?
    publicKey    String
    userId       String
    user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
    credentialID String
    counter      Int
    deviceType   String
    backedUp     Boolean
    transports   String?
    createdAt    DateTime?

    @@map("passkey")
}

model TwoFactor {
    id          String @id @default(cuid())
    secret      String
    backupCodes String
    userId      String
    user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)

    @@map("twoFactor")
}

model Organization {
    id                 String       @id @default(cuid())
    name               String
    slug               String?
    logo               String?
    createdAt          DateTime
    metadata           String?
    paymentsCustomerId String?
    members            Member[]
    invitations        Invitation[]
    purchases          Purchase[]
    aiChats            AiChat[]
    divisions          Division[]

    @@unique([slug])
    @@map("organization")
}

model Member {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String
    user           User         @relation(fields: [userId], references: [id], onDelete: Cascade)
    role           String
    createdAt      DateTime

    @@unique([organizationId, userId])
    @@map("member")
}

model Invitation {
    id             String       @id @default(cuid())
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    email          String
    role           String?
    status         String
    expiresAt      DateTime
    inviterId      String
    user           User         @relation(fields: [inviterId], references: [id], onDelete: Cascade)

    @@map("invitation")
}

enum PurchaseType {
    SUBSCRIPTION
    ONE_TIME
}

model Purchase {
    id             String        @id @default(cuid())
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    organizationId String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    userId         String?
    type           PurchaseType
    customerId     String
    subscriptionId String?       @unique
    productId      String
    status         String?
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@index([subscriptionId])
    @@map("purchase")
}

model AiChat {
    id             String        @id @default(cuid())
    organizationId String?
    organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    userId         String?
    user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
    title          String?
    /// [Array<{role: "user" | "assistant"; content: string;}>]
    messages       Json          @default("[]") /// @zod.custom.use(z.array(z.object({ role: z.enum(['user', 'assistant']), content: z.string() })))
    createdAt      DateTime      @default(now())
    updatedAt      DateTime      @updatedAt

    @@map("ai_chat")
}

model Division {
    id             String       @id @default(cuid())
    name           String
    description    String?
    organizationId String
    organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
    teams          Team[]
    createdAt      DateTime     @default(now())
    updatedAt      DateTime     @updatedAt

    @@unique([organizationId, name])
    @@map("division")
}

model Team {
    id          String    @id @default(cuid())
    name        String
    description String?
    divisionId  String
    division    Division  @relation(fields: [divisionId], references: [id], onDelete: Cascade)
    players     Player[]
    points      Int       @default(0)
    createdAt   DateTime  @default(now())
    updatedAt   DateTime  @updatedAt
    stats       TeamStats[]

    @@unique([divisionId, name])
    @@map("team")
}

model Player {
    id          String        @id @default(cuid())
    name        String
    email       String?
    phone       String?
    teamId      String
    team        Team          @relation(fields: [teamId], references: [id], onDelete: Cascade)
    userId      String?       // Optional link to User if they have an account
    user        User?         @relation(fields: [userId], references: [id], onDelete: SetNull)
    stats       PlayerStats[]
    active      Boolean       @default(true)
    createdAt   DateTime      @default(now())
    updatedAt   DateTime      @updatedAt

    @@unique([teamId, email])
    @@map("player")
}

model PlayerStats {
    id              String   @id @default(cuid())
    playerId        String
    player          Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)
    season          String?  // Optional season identifier
    gamesPlayed     Int      @default(0)
    gamesWon        Int      @default(0)
    gamesLost       Int      @default(0)
    matchesPlayed   Int      @default(0)
    matchesWon      Int      @default(0)
    matchesLost     Int      @default(0)
    winPercentage   Float?   // Calculated field
    skillLevel      Int?     // Pool skill level (e.g., 1-9 for 9-ball)
    handicap        Int?     // Handicap for league play
    createdAt       DateTime @default(now())
    updatedAt       DateTime @updatedAt

    @@unique([playerId, season])
    @@map("player_stats")
}