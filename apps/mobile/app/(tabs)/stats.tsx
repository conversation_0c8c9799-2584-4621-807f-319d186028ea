import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ScrollView,
  TouchableOpacity,
  Platform,
  Alert,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface TeamStats {
  teamId: string;
  teamName: string;
  totalGames: number;
  wins: number;
  losses: number;
  winPercentage: number;
}

interface PlayerStats {
  playerId: string;
  playerName: string;
  teamName: string;
  wins: number;
  losses: number;
  totalMatches: number;
  winPercentage: number;
}

export default function StatsScreen() {
  const teams = useQuery(api.teams.list);
  const games = useQuery(api.games.list);

  const getTeamStats = (): TeamStats[] => {
    if (!teams || !games) return [];

    return teams.map(team => {
      const teamGames = games.filter(game => 
        game.homeTeamId === team._id || game.awayTeamId === team._id
      );
      
      const completedGames = teamGames.filter(game => game.isCompleted);
      
      const wins = completedGames.filter(game => {
        if (game.homeTeamId === team._id) {
          return (game.homeScore || 0) > (game.awayScore || 0);
        } else {
          return (game.awayScore || 0) > (game.homeScore || 0);
        }
      }).length;
      
      const losses = completedGames.length - wins;
      const winPercentage = completedGames.length > 0 ? (wins / completedGames.length) * 100 : 0;

      return {
        teamId: team._id,
        teamName: team.name,
        totalGames: completedGames.length,
        wins,
        losses,
        winPercentage: Math.round(winPercentage),
      };
    }).sort((a, b) => b.winPercentage - a.winPercentage);
  };

  const renderTeamStatsCard = ({ item: stats }: { item: TeamStats }) => (
    <View style={styles.statsCard}>
      <View style={styles.statsHeader}>
        <View style={styles.teamIcon}>
          <Ionicons name="people" size={20} color="#007AFF" />
        </View>
        <View style={styles.statsInfo}>
          <Text style={styles.teamName}>{stats.teamName}</Text>
          <Text style={styles.gamesPlayed}>{stats.totalGames} games played</Text>
        </View>
        <View style={styles.winPercentageBadge}>
          <Text style={styles.winPercentageText}>{stats.winPercentage}%</Text>
        </View>
      </View>
      
      <View style={styles.statsRow}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.wins}</Text>
          <Text style={styles.statLabel}>Wins</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.losses}</Text>
          <Text style={styles.statLabel}>Losses</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{stats.totalGames}</Text>
          <Text style={styles.statLabel}>Total</Text>
        </View>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="bar-chart-outline" size={64} color="#C7C7CC" />
      <Text style={styles.emptyTitle}>No Statistics Yet</Text>
      <Text style={styles.emptyDescription}>
        Complete some games to see team and player statistics
      </Text>
    </View>
  );

  const renderOverviewCard = () => {
    if (!teams || !games) return null;

    const totalTeams = teams.length;
    const totalGames = games.length;
    const completedGames = games.filter(game => game.isCompleted).length;
    const upcomingGames = totalGames - completedGames;

    return (
      <View style={styles.overviewCard}>
        <View style={styles.overviewHeader}>
          <Text style={styles.overviewTitle}>League Overview</Text>
          <TouchableOpacity
            style={styles.playerRankingsButton}
            onPress={() => {
              if (Platform.OS !== 'web') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
              try {
                router.navigate('/player-stats');
              } catch (error) {
                console.error('Navigation error:', error);
                Alert.alert('Navigation Error', 'Could not navigate to player stats');
              }
            }}
          >
            <Ionicons name="podium" size={16} color="#007AFF" />
            <Text style={styles.playerRankingsText}>Player Rankings</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.overviewGrid}>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewValue}>{totalTeams}</Text>
            <Text style={styles.overviewLabel}>Teams</Text>
          </View>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewValue}>{completedGames}</Text>
            <Text style={styles.overviewLabel}>Completed</Text>
          </View>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewValue}>{upcomingGames}</Text>
            <Text style={styles.overviewLabel}>Upcoming</Text>
          </View>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewValue}>{totalGames}</Text>
            <Text style={styles.overviewLabel}>Total Games</Text>
          </View>
        </View>
      </View>
    );
  };

  if (teams === undefined || games === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading statistics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const teamStats = getTeamStats();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Statistics</Text>
      </View>

      <ScrollView 
        style={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderOverviewCard()}
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Team Rankings</Text>
          {teamStats.length === 0 ? (
            renderEmptyState()
          ) : (
            <FlatList
              data={teamStats}
              renderItem={renderTeamStatsCard}
              keyExtractor={(item) => item.teamId}
              scrollEnabled={false}
              showsVerticalScrollIndicator={false}
            />
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  title: {
    fontSize: 34,
    fontWeight: 'bold',
    color: '#000000',
  },
  scrollContainer: {
    flex: 1,
  },
  overviewCard: {
    backgroundColor: '#FFFFFF',
    margin: 20,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  overviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  overviewTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
  },
  playerRankingsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    gap: 6,
  },
  playerRankingsText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  overviewItem: {
    width: '50%',
    alignItems: 'center',
    paddingVertical: 12,
  },
  overviewValue: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 4,
  },
  overviewLabel: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 16,
  },
  statsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  teamIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  statsInfo: {
    flex: 1,
  },
  teamName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  gamesPlayed: {
    fontSize: 14,
    color: '#8E8E93',
  },
  winPercentageBadge: {
    backgroundColor: '#34C759',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  winPercentageText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  statsRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#8E8E93',
    fontWeight: '500',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  statDivider: {
    width: 1,
    height: 40,
    backgroundColor: '#E5E5EA',
    marginHorizontal: 16,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 40,
  },
});