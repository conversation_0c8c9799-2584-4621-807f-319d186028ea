import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  TextInput,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';

interface Game {
  _id: Id<'games'>;
  homeTeam: string;
  awayTeam: string;
  homeTeamId: Id<'teams'>;
  awayTeamId: Id<'teams'>;
  date: number;
  location?: string;
  isCompleted: boolean;
  homeScore?: number;
  awayScore?: number;
}

interface Team {
  _id: Id<'teams'>;
  name: string;
  description?: string;
  memberCount: number;
}

export default function GamesScreen() {
  const games = useQuery(api.games.list);
  const teams = useQuery(api.teams.list);
  const createGame = useMutation(api.games.create);
  const updateGame = useMutation(api.games.update);
  const deleteGame = useMutation(api.games.remove);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingGame, setEditingGame] = useState<Game | null>(null);
  const [homeTeamId, setHomeTeamId] = useState<Id<'teams'> | ''>('');
  const [awayTeamId, setAwayTeamId] = useState<Id<'teams'> | ''>('');
  const [location, setLocation] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Bottom sheet states
  const [showHomeTeamSheet, setShowHomeTeamSheet] = useState(false);
  const [showAwayTeamSheet, setShowAwayTeamSheet] = useState(false);
  const [showEditHomeTeamSheet, setShowEditHomeTeamSheet] = useState(false);
  const [showEditAwayTeamSheet, setShowEditAwayTeamSheet] = useState(false);

  const getTeamName = (teamId: Id<'teams'> | '') => {
    if (!teamId || !teams) return 'Select team';
    const team = teams.find(t => t._id === teamId);
    return team?.name || 'Select team';
  };

  const handleCreateGame = async () => {
    if (!homeTeamId || !awayTeamId) {
      Alert.alert('Error', 'Please select both teams');
      return;
    }

    if (homeTeamId === awayTeamId) {
      Alert.alert('Error', 'Please select different teams');
      return;
    }

    setIsCreating(true);
    try {
      await createGame({
        homeTeamId: homeTeamId as Id<'teams'>,
        awayTeamId: awayTeamId as Id<'teams'>,
        date: Date.now(),
        location: location.trim() || undefined,
      });
      
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      
      setShowCreateModal(false);
      setHomeTeamId('');
      setAwayTeamId('');
      setLocation('');
    } catch (error) {
      console.error('Failed to create game:', error);
      Alert.alert('Error', 'Failed to create game');
    } finally {
      setIsCreating(false);
    }
  };

  const handleEditGame = (game: Game) => {
    setEditingGame(game);
    setHomeTeamId(game.homeTeamId);
    setAwayTeamId(game.awayTeamId);
    setLocation(game.location || '');
    setShowEditModal(true);
  };

  const handleUpdateGame = async () => {
    if (!editingGame || !homeTeamId || !awayTeamId) {
      Alert.alert('Error', 'Please select both teams');
      return;
    }

    if (homeTeamId === awayTeamId) {
      Alert.alert('Error', 'Please select different teams');
      return;
    }

    setIsUpdating(true);
    try {
      await updateGame({
        id: editingGame._id,
        homeTeamId: homeTeamId as Id<'teams'>,
        awayTeamId: awayTeamId as Id<'teams'>,
        location: location.trim() || undefined,
      });
      
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      
      setShowEditModal(false);
      setEditingGame(null);
      setHomeTeamId('');
      setAwayTeamId('');
      setLocation('');
    } catch (error) {
      console.error('Failed to update game:', error);
      Alert.alert('Error', 'Failed to update game');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteGame = (game: Game) => {
    Alert.alert(
      'Delete Game',
      `Are you sure you want to delete this game between ${game.homeTeam} and ${game.awayTeam}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteGame({ id: game._id });
              if (Platform.OS !== 'web') {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              }
            } catch (error) {
              console.error('Failed to delete game:', error);
              Alert.alert('Error', 'Failed to delete game');
            }
          },
        },
      ]
    );
  };

  const showGameOptions = (game: Game) => {
    Alert.alert(
      `${game.homeTeam} vs ${game.awayTeam}`,
      'Choose an action',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'View Details',
          onPress: () => {
            console.log('Navigating to game from alert:', game._id);
            try {
              router.navigate(`/game/${game._id}`);
            } catch (error) {
              console.error('Navigation error:', error);
              Alert.alert('Navigation Error', 'Could not navigate to game details');
            }
          },
        },
        {
          text: 'Edit',
          onPress: () => handleEditGame(game),
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => handleDeleteGame(game),
        },
      ]
    );
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const renderGameCard = ({ item: game }: { item: Game }) => (
    <TouchableOpacity
      style={styles.gameCard}
      onPress={() => {
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        console.log('Navigating to game:', game._id);
        try {
          router.navigate(`/game/${game._id}`);
        } catch (error) {
          console.error('Navigation error:', error);
          Alert.alert('Navigation Error', 'Could not navigate to game details');
        }
      }}
      onLongPress={() => {
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
        }
        showGameOptions(game);
      }}
    >
      <View style={styles.gameHeader}>
        <Text style={styles.gameDate}>{formatDate(game.date)}</Text>
        <View style={styles.gameHeaderActions}>
          {game.location && (
            <View style={styles.locationBadge}>
              <Ionicons name="location" size={12} color="#8E8E93" />
              <Text style={styles.locationText}>{game.location}</Text>
            </View>
          )}
          <TouchableOpacity
            style={styles.optionsButton}
            onPress={() => {
              if (Platform.OS !== 'web') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
              showGameOptions(game);
            }}
          >
            <Ionicons name="ellipsis-horizontal" size={16} color="#8E8E93" />
          </TouchableOpacity>
        </View>
      </View>
      
      <View style={styles.matchup}>
        <View style={styles.teamContainer}>
          <Text style={styles.teamName}>{game.homeTeam}</Text>
          <Text style={styles.homeLabel}>HOME</Text>
        </View>
        
        <View style={styles.scoreContainer}>
          {game.isCompleted ? (
            <>
              <Text style={styles.score}>{game.homeScore}</Text>
              <Text style={styles.scoreSeparator}>-</Text>
              <Text style={styles.score}>{game.awayScore}</Text>
            </>
          ) : (
            <Text style={styles.vsText}>VS</Text>
          )}
        </View>
        
        <View style={[styles.teamContainer, styles.awayTeam]}>
          <Text style={styles.teamName}>{game.awayTeam}</Text>
          <Text style={styles.awayLabel}>AWAY</Text>
        </View>
      </View>
      
      <View style={styles.gameFooter}>
        <View style={[
          styles.statusBadge,
          game.isCompleted ? styles.completedBadge : styles.scheduledBadge
        ]}>
          <Text style={[
            styles.statusText,
            game.isCompleted ? styles.completedText : styles.scheduledText
          ]}>
            {game.isCompleted ? 'Completed' : 'Scheduled'}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="trophy-outline" size={64} color="#C7C7CC" />
      <Text style={styles.emptyTitle}>No Games Scheduled</Text>
      <Text style={styles.emptyDescription}>
        Create your first game to start tracking matches
      </Text>
    </View>
  );

  const renderTeamSelector = (
    title: string,
    selectedTeamId: Id<'teams'> | '',
    onSelect: (teamId: Id<'teams'>) => void,
    onClose: () => void
  ) => (
    <View style={styles.bottomSheetContainer}>
      <View style={styles.bottomSheetHeader}>
        <Text style={styles.bottomSheetTitle}>{title}</Text>
        <TouchableOpacity onPress={onClose} style={styles.bottomSheetCloseButton}>
          <Ionicons name="close" size={24} color="#8E8E93" />
        </TouchableOpacity>
      </View>
      <FlatList
        data={teams || []}
        keyExtractor={(item) => item._id}
        renderItem={({ item: team }) => (
          <TouchableOpacity
            style={[
              styles.teamSelectorItem,
              selectedTeamId === team._id && styles.selectedTeamItem
            ]}
            onPress={() => {
              if (Platform.OS !== 'web') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
              onSelect(team._id);
              onClose();
            }}
          >
            <View style={styles.teamSelectorInfo}>
              <Text style={[
                styles.teamSelectorName,
                selectedTeamId === team._id && styles.selectedTeamName
              ]}>
                {team.name}
              </Text>
              {team.description && (
                <Text style={styles.teamSelectorDescription}>{team.description}</Text>
              )}
            </View>
            <View style={styles.teamSelectorMeta}>
              <Text style={styles.memberCountBadge}>{team.memberCount}</Text>
              {selectedTeamId === team._id && (
                <Ionicons name="checkmark-circle" size={20} color="#007AFF" />
              )}
            </View>
          </TouchableOpacity>
        )}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );

  if (games === undefined || teams === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading games...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Games</Text>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            if (teams.length < 2) {
              Alert.alert('Not Enough Teams', 'You need at least 2 teams to create a game');
              return;
            }
            if (Platform.OS !== 'web') {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }
            setShowCreateModal(true);
          }}
        >
          <Ionicons name="add" size={24} color="#FFFFFF" />
        </TouchableOpacity>
      </View>

      <FlatList
        data={games}
        renderItem={renderGameCard}
        keyExtractor={(item) => item._id}
        contentContainerStyle={[
          styles.listContainer,
          games.length === 0 && styles.emptyListContainer,
        ]}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {/* Create Game Modal */}
      <Modal
        visible={showCreateModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => {
                setShowCreateModal(false);
                setHomeTeamId('');
                setAwayTeamId('');
                setLocation('');
              }}
            >
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>New Game</Text>
            <TouchableOpacity
              onPress={handleCreateGame}
              disabled={isCreating || !homeTeamId || !awayTeamId}
            >
              <Text
                style={[
                  styles.createButton,
                  (!homeTeamId || !awayTeamId || isCreating) && styles.disabledButton,
                ]}
              >
                {isCreating ? 'Creating...' : 'Create'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Home Team</Text>
              <TouchableOpacity
                style={styles.teamSelectorButton}
                onPress={() => setShowHomeTeamSheet(true)}
              >
                <Text style={[
                  styles.teamSelectorButtonText,
                  !homeTeamId && styles.placeholderText
                ]}>
                  {getTeamName(homeTeamId)}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Away Team</Text>
              <TouchableOpacity
                style={styles.teamSelectorButton}
                onPress={() => setShowAwayTeamSheet(true)}
              >
                <Text style={[
                  styles.teamSelectorButtonText,
                  !awayTeamId && styles.placeholderText
                ]}>
                  {getTeamName(awayTeamId)}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Location (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={location}
                onChangeText={setLocation}
                placeholder="Enter game location"
                maxLength={100}
              />
            </View>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Edit Game Modal */}
      <Modal
        visible={showEditModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => {
                setShowEditModal(false);
                setEditingGame(null);
                setHomeTeamId('');
                setAwayTeamId('');
                setLocation('');
              }}
            >
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Game</Text>
            <TouchableOpacity
              onPress={handleUpdateGame}
              disabled={isUpdating || !homeTeamId || !awayTeamId}
            >
              <Text
                style={[
                  styles.createButton,
                  (!homeTeamId || !awayTeamId || isUpdating) && styles.disabledButton,
                ]}
              >
                {isUpdating ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Home Team</Text>
              <TouchableOpacity
                style={styles.teamSelectorButton}
                onPress={() => setShowEditHomeTeamSheet(true)}
              >
                <Text style={[
                  styles.teamSelectorButtonText,
                  !homeTeamId && styles.placeholderText
                ]}>
                  {getTeamName(homeTeamId)}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Away Team</Text>
              <TouchableOpacity
                style={styles.teamSelectorButton}
                onPress={() => setShowEditAwayTeamSheet(true)}
              >
                <Text style={[
                  styles.teamSelectorButtonText,
                  !awayTeamId && styles.placeholderText
                ]}>
                  {getTeamName(awayTeamId)}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Location (Optional)</Text>
              <TextInput
                style={styles.textInput}
                value={location}
                onChangeText={setLocation}
                placeholder="Enter game location"
                maxLength={100}
              />
            </View>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Home Team Selection Bottom Sheet - Create */}
      <Modal
        visible={showHomeTeamSheet}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.bottomSheetModal}>
          {renderTeamSelector(
            'Select Home Team',
            homeTeamId,
            setHomeTeamId,
            () => setShowHomeTeamSheet(false)
          )}
        </SafeAreaView>
      </Modal>

      {/* Away Team Selection Bottom Sheet - Create */}
      <Modal
        visible={showAwayTeamSheet}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.bottomSheetModal}>
          {renderTeamSelector(
            'Select Away Team',
            awayTeamId,
            setAwayTeamId,
            () => setShowAwayTeamSheet(false)
          )}
        </SafeAreaView>
      </Modal>

      {/* Home Team Selection Bottom Sheet - Edit */}
      <Modal
        visible={showEditHomeTeamSheet}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.bottomSheetModal}>
          {renderTeamSelector(
            'Select Home Team',
            homeTeamId,
            setHomeTeamId,
            () => setShowEditHomeTeamSheet(false)
          )}
        </SafeAreaView>
      </Modal>

      {/* Away Team Selection Bottom Sheet - Edit */}
      <Modal
        visible={showEditAwayTeamSheet}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.bottomSheetModal}>
          {renderTeamSelector(
            'Select Away Team',
            awayTeamId,
            setAwayTeamId,
            () => setShowEditAwayTeamSheet(false)
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  title: {
    fontSize: 34,
    fontWeight: 'bold',
    color: '#000000',
  },
  addButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#007AFF',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  listContainer: {
    padding: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  gameCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  gameHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  gameDate: {
    fontSize: 14,
    fontWeight: '600',
    color: '#8E8E93',
  },
  gameHeaderActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  locationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F2F2F7',
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  locationText: {
    fontSize: 12,
    color: '#8E8E93',
    marginLeft: 4,
  },
  optionsButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  matchup: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  teamContainer: {
    flex: 1,
    alignItems: 'center',
  },
  awayTeam: {
    alignItems: 'center',
  },
  teamName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 4,
  },
  homeLabel: {
    fontSize: 10,
    fontWeight: '600',
    color: '#34C759',
    letterSpacing: 0.5,
  },
  awayLabel: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FF9500',
    letterSpacing: 0.5,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  score: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  scoreSeparator: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#8E8E93',
    marginHorizontal: 8,
  },
  vsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8E8E93',
  },
  gameFooter: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  completedBadge: {
    backgroundColor: '#E8F5E8',
  },
  scheduledBadge: {
    backgroundColor: '#FFF3E0',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  completedText: {
    color: '#34C759',
  },
  scheduledText: {
    color: '#FF9500',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 40,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  cancelButton: {
    fontSize: 17,
    color: '#007AFF',
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000000',
  },
  createButton: {
    fontSize: 17,
    fontWeight: '600',
    color: '#007AFF',
  },
  disabledButton: {
    color: '#C7C7CC',
  },
  modalContent: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
  },
  teamSelectorButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  teamSelectorButtonText: {
    fontSize: 16,
    color: '#000000',
  },
  placeholderText: {
    color: '#8E8E93',
  },
  bottomSheetModal: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  bottomSheetContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  bottomSheetCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  teamSelectorItem: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedTeamItem: {
    backgroundColor: '#E3F2FD',
  },
  teamSelectorInfo: {
    flex: 1,
  },
  teamSelectorName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  selectedTeamName: {
    color: '#007AFF',
  },
  teamSelectorDescription: {
    fontSize: 14,
    color: '#8E8E93',
  },
  teamSelectorMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  memberCountBadge: {
    fontSize: 14,
    fontWeight: '600',
    color: '#8E8E93',
    backgroundColor: '#F2F2F7',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
});