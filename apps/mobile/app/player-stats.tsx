import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { router } from 'expo-router';
import * as Haptics from 'expo-haptics';

type SortOption = 'winPercentage' | 'gamesWon' | 'playerPoints' | 'breakAndRuns';

interface PlayerRanking {
  playerId: string;
  memberName: string;
  teamName: string;
  email?: string;
  gamesPlayed: number;
  gamesWon: number;
  winPercentage: number;
  playerPoints: number;
  oppPoints: number;
  breakAndRuns: number;
  oppRunOuts: number;
  tableRuns: number;
  weeksPlayed: number;
}

export default function PlayerStatsScreen() {
  const [sortBy, setSortBy] = useState<SortOption>('winPercentage');
  
  const playerRankings = useQuery(api.playerStats.getPlayerRankings, { 
    sortBy 
  }) as PlayerRanking[] | undefined;

  const sortOptions = [
    { key: 'winPercentage' as SortOption, label: 'Win %', icon: 'trophy' },
    { key: 'gamesWon' as SortOption, label: 'Wins', icon: 'checkmark-circle' },
    { key: 'playerPoints' as SortOption, label: 'Points', icon: 'star' },
    { key: 'breakAndRuns' as SortOption, label: 'B&R', icon: 'flash' },
  ];

  const handleSortChange = (newSort: SortOption) => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
    setSortBy(newSort);
  };

  const renderSortButton = (option: { key: SortOption; label: string; icon: string }) => (
    <TouchableOpacity
      key={option.key}
      style={[
        styles.sortButton,
        sortBy === option.key && styles.activeSortButton
      ]}
      onPress={() => handleSortChange(option.key)}
    >
      <Ionicons 
        name={option.icon as any} 
        size={16} 
        color={sortBy === option.key ? '#FFFFFF' : '#007AFF'} 
      />
      <Text style={[
        styles.sortButtonText,
        sortBy === option.key && styles.activeSortButtonText
      ]}>
        {option.label}
      </Text>
    </TouchableOpacity>
  );

  const renderPlayerCard = ({ item: player, index }: { item: PlayerRanking; index: number }) => (
    <View style={styles.playerCard}>
      <View style={styles.playerHeader}>
        <View style={styles.rankBadge}>
          <Text style={styles.rankText}>#{index + 1}</Text>
        </View>
        <View style={styles.playerInfo}>
          <Text style={styles.playerName}>{player.memberName}</Text>
          <Text style={styles.teamName}>{player.teamName}</Text>
        </View>
        <View style={styles.primaryStat}>
          <Text style={styles.primaryStatValue}>
            {sortBy === 'winPercentage' && `${player.winPercentage.toFixed(1)}%`}
            {sortBy === 'gamesWon' && player.gamesWon}
            {sortBy === 'playerPoints' && player.playerPoints}
            {sortBy === 'breakAndRuns' && player.breakAndRuns}
          </Text>
          <Text style={styles.primaryStatLabel}>
            {sortOptions.find(opt => opt.key === sortBy)?.label}
          </Text>
        </View>
      </View>
      
      <View style={styles.statsGrid}>
        <View style={styles.statBox}>
          <Text style={styles.statBoxValue}>{player.gamesPlayed}</Text>
          <Text style={styles.statBoxLabel}>Games</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statBoxValue}>{player.gamesWon}</Text>
          <Text style={styles.statBoxLabel}>Wins</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statBoxValue}>{player.winPercentage.toFixed(1)}%</Text>
          <Text style={styles.statBoxLabel}>Win %</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statBoxValue}>{player.weeksPlayed}</Text>
          <Text style={styles.statBoxLabel}>Weeks</Text>
        </View>
      </View>
      
      <View style={styles.detailedStats}>
        <View style={styles.detailedStatRow}>
          <Text style={styles.detailedStatLabel}>Player Points:</Text>
          <Text style={styles.detailedStatValue}>{player.playerPoints}</Text>
        </View>
        <View style={styles.detailedStatRow}>
          <Text style={styles.detailedStatLabel}>Opponent Points:</Text>
          <Text style={styles.detailedStatValue}>{player.oppPoints}</Text>
        </View>
        <View style={styles.detailedStatRow}>
          <Text style={styles.detailedStatLabel}>Break & Runs:</Text>
          <Text style={styles.detailedStatValue}>{player.breakAndRuns}</Text>
        </View>
        <View style={styles.detailedStatRow}>
          <Text style={styles.detailedStatLabel}>Table Runs:</Text>
          <Text style={styles.detailedStatValue}>{player.tableRuns}</Text>
        </View>
        <View style={styles.detailedStatRow}>
          <Text style={styles.detailedStatLabel}>Opp. Run Outs:</Text>
          <Text style={styles.detailedStatValue}>{player.oppRunOuts}</Text>
        </View>
      </View>
    </View>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="podium-outline" size={64} color="#C7C7CC" />
      <Text style={styles.emptyTitle}>No Player Statistics</Text>
      <Text style={styles.emptyDescription}>
        Complete some matches to see player rankings and statistics
      </Text>
    </View>
  );

  if (playerRankings === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading player statistics...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            if (Platform.OS !== 'web') {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }
            router.back();
          }}
        >
          <Ionicons name="chevron-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.title}>Player Rankings</Text>
        <View style={styles.headerSpacer} />
      </View>

      <View style={styles.sortContainer}>
        {sortOptions.map(renderSortButton)}
      </View>

      <FlatList
        data={playerRankings}
        renderItem={renderPlayerCard}
        keyExtractor={(item) => item.playerId}
        contentContainerStyle={[
          styles.listContainer,
          playerRankings.length === 0 && styles.emptyListContainer,
        ]}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    flex: 1,
    fontSize: 20,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
  },
  headerSpacer: {
    width: 44,
  },
  sortContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
    gap: 12,
  },
  sortButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#F2F2F7',
    borderWidth: 1,
    borderColor: '#007AFF',
    gap: 6,
  },
  activeSortButton: {
    backgroundColor: '#007AFF',
  },
  sortButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#007AFF',
  },
  activeSortButtonText: {
    color: '#FFFFFF',
  },
  listContainer: {
    padding: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  playerCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  playerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  rankBadge: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  rankText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
  },
  playerInfo: {
    flex: 1,
  },
  playerName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  teamName: {
    fontSize: 14,
    color: '#8E8E93',
  },
  primaryStat: {
    alignItems: 'center',
  },
  primaryStatValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#007AFF',
  },
  primaryStatLabel: {
    fontSize: 12,
    color: '#8E8E93',
    marginTop: 2,
  },
  statsGrid: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 12,
  },
  statBox: {
    flex: 1,
    backgroundColor: '#F2F2F7',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
  },
  statBoxValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  statBoxLabel: {
    fontSize: 12,
    color: '#8E8E93',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  detailedStats: {
    borderTopWidth: 1,
    borderTopColor: '#E5E5EA',
    paddingTop: 16,
  },
  detailedStatRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  detailedStatLabel: {
    fontSize: 14,
    color: '#8E8E93',
  },
  detailedStatValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000000',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 40,
  },
});