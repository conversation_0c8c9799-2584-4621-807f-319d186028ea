import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  Modal,
  Platform,
  ScrollView,
  TextInput,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useLocalSearchParams, router } from 'expo-router';
import * as Haptics from 'expo-haptics';

interface Match {
  _id: Id<'matches'>;
  homePlayer: string;
  awayPlayer: string;
  homePlayerId: Id<'members'>;
  awayPlayerId: Id<'members'>;
  winner?: 'home' | 'away';
  homeRacks?: number;
  awayRacks?: number;
  matchNumber: number;
}

interface GameWithDetails {
  _id: Id<'games'>;
  homeTeam: string;
  awayTeam: string;
  homeTeamId: Id<'teams'>;
  awayTeamId: Id<'teams'>;
  date: number;
  location?: string;
  isCompleted: boolean;
  homeScore?: number;
  awayScore?: number;
  matches: Match[];
}

interface Member {
  _id: Id<'members'>;
  name: string;
  email?: string;
}

export default function GameDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const gameId = id as Id<'games'>;

  const game = useQuery(api.games.get, { id: gameId }) as GameWithDetails | undefined | null;
  const homeMembers = useQuery(api.members.listByTeam, 
    game ? { teamId: game.homeTeamId } : "skip"
  );
  const awayMembers = useQuery(api.members.listByTeam, 
    game ? { teamId: game.awayTeamId } : "skip"
  );
  
  const createMatch = useMutation(api.matches.create);
  const updateMatchResult = useMutation(api.matches.updateResult);
  const updateGameScore = useMutation(api.games.updateScore);

  const [showAddMatchModal, setShowAddMatchModal] = useState(false);
  const [homePlayerId, setHomePlayerId] = useState<Id<'members'> | ''>('');
  const [awayPlayerId, setAwayPlayerId] = useState<Id<'members'> | ''>('');
  const [isCreating, setIsCreating] = useState(false);

  const [showResultModal, setShowResultModal] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [homeRacks, setHomeRacks] = useState('');
  const [awayRacks, setAwayRacks] = useState('');
  
  // Enhanced statistics tracking
  const [homePlayerPoints, setHomePlayerPoints] = useState('');
  const [awayPlayerPoints, setAwayPlayerPoints] = useState('');
  const [homeBreakAndRuns, setHomeBreakAndRuns] = useState('');
  const [awayBreakAndRuns, setAwayBreakAndRuns] = useState('');
  const [homeTableRuns, setHomeTableRuns] = useState('');
  const [awayTableRuns, setAwayTableRuns] = useState('');
  const [homeOppRunOuts, setHomeOppRunOuts] = useState('');
  const [awayOppRunOuts, setAwayOppRunOuts] = useState('');

  // Bottom sheet states
  const [showHomePlayerSheet, setShowHomePlayerSheet] = useState(false);
  const [showAwayPlayerSheet, setShowAwayPlayerSheet] = useState(false);

  const getPlayerName = (playerId: Id<'members'> | '', isHome: boolean) => {
    if (!playerId) return 'Select player';
    const members = isHome ? homeMembers : awayMembers;
    if (!members) return 'Select player';
    const player = members.find(m => m._id === playerId);
    return player?.name || 'Select player';
  };

  const handleAddMatch = async () => {
    if (!homePlayerId || !awayPlayerId) {
      Alert.alert('Error', 'Please select both players');
      return;
    }

    if (homePlayerId === awayPlayerId) {
      Alert.alert('Error', 'Please select different players');
      return;
    }

    setIsCreating(true);
    try {
      const nextMatchNumber = (game?.matches.length || 0) + 1;
      await createMatch({
        gameId,
        homePlayerId: homePlayerId as Id<'members'>,
        awayPlayerId: awayPlayerId as Id<'members'>,
        matchNumber: nextMatchNumber,
      });
      
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      
      setShowAddMatchModal(false);
      setHomePlayerId('');
      setAwayPlayerId('');
    } catch (error) {
      console.error('Failed to create match:', error);
      Alert.alert('Error', 'Failed to create match');
    } finally {
      setIsCreating(false);
    }
  };

  const handleUpdateResult = async () => {
    if (!selectedMatch || !homeRacks || !awayRacks) {
      Alert.alert('Error', 'Please enter both rack counts');
      return;
    }

    const homeRacksNum = parseInt(homeRacks);
    const awayRacksNum = parseInt(awayRacks);

    if (isNaN(homeRacksNum) || isNaN(awayRacksNum) || homeRacksNum < 0 || awayRacksNum < 0) {
      Alert.alert('Error', 'Please enter valid rack counts');
      return;
    }

    if (homeRacksNum === awayRacksNum) {
      Alert.alert('Error', 'There must be a winner');
      return;
    }

    try {
      const winner = homeRacksNum > awayRacksNum ? 'home' : 'away';
      await updateMatchResult({
        id: selectedMatch._id,
        winner,
        homeRacks: homeRacksNum,
        awayRacks: awayRacksNum,
        // Enhanced statistics
        homePlayerPoints: parseInt(homePlayerPoints) || 0,
        awayPlayerPoints: parseInt(awayPlayerPoints) || 0,
        homeBreakAndRuns: parseInt(homeBreakAndRuns) || 0,
        awayBreakAndRuns: parseInt(awayBreakAndRuns) || 0,
        homeTableRuns: parseInt(homeTableRuns) || 0,
        awayTableRuns: parseInt(awayTableRuns) || 0,
        homeOppRunOuts: parseInt(homeOppRunOuts) || 0,
        awayOppRunOuts: parseInt(awayOppRunOuts) || 0,
      });

      // Update game score if all matches are completed
      if (game) {
        const updatedMatches = game.matches.map(match => 
          match._id === selectedMatch._id 
            ? { ...match, winner, homeRacks: homeRacksNum, awayRacks: awayRacksNum }
            : match
        );

        const completedMatches = updatedMatches.filter(match => match.winner);
        if (completedMatches.length === updatedMatches.length && updatedMatches.length > 0) {
          const homeWins = completedMatches.filter(match => match.winner === 'home').length;
          const awayWins = completedMatches.filter(match => match.winner === 'away').length;
          
          await updateGameScore({
            id: gameId,
            homeScore: homeWins,
            awayScore: awayWins,
            isCompleted: true,
          });
        }
      }
      
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      
      setShowResultModal(false);
      setSelectedMatch(null);
      resetResultForm();
    } catch (error) {
      console.error('Failed to update match result:', error);
      Alert.alert('Error', 'Failed to update match result');
    }
  };

  const resetResultForm = () => {
    setHomeRacks('');
    setAwayRacks('');
    setHomePlayerPoints('');
    setAwayPlayerPoints('');
    setHomeBreakAndRuns('');
    setAwayBreakAndRuns('');
    setHomeTableRuns('');
    setAwayTableRuns('');
    setHomeOppRunOuts('');
    setAwayOppRunOuts('');
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const renderMatchCard = ({ item: match }: { item: Match }) => (
    <TouchableOpacity
      style={styles.matchCard}
      onPress={() => {
        if (Platform.OS !== 'web') {
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        }
        setSelectedMatch(match);
        setHomeRacks(match.homeRacks?.toString() || '');
        setAwayRacks(match.awayRacks?.toString() || '');
        setShowResultModal(true);
      }}
    >
      <View style={styles.matchHeader}>
        <Text style={styles.matchNumber}>Match {match.matchNumber}</Text>
        {match.winner && (
          <View style={[
            styles.statusBadge,
            styles.completedBadge
          ]}>
            <Text style={styles.completedText}>Completed</Text>
          </View>
        )}
      </View>
      
      <View style={styles.matchup}>
        <View style={styles.playerContainer}>
          <Text style={styles.playerName}>{match.homePlayer}</Text>
          <Text style={styles.homeLabel}>HOME</Text>
        </View>
        
        <View style={styles.scoreContainer}>
          {match.winner ? (
            <>
              <Text style={[
                styles.score,
                match.winner === 'home' && styles.winningScore
              ]}>
                {match.homeRacks}
              </Text>
              <Text style={styles.scoreSeparator}>-</Text>
              <Text style={[
                styles.score,
                match.winner === 'away' && styles.winningScore
              ]}>
                {match.awayRacks}
              </Text>
            </>
          ) : (
            <Text style={styles.vsText}>VS</Text>
          )}
        </View>
        
        <View style={[styles.playerContainer, styles.awayPlayer]}>
          <Text style={styles.playerName}>{match.awayPlayer}</Text>
          <Text style={styles.awayLabel}>AWAY</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="people-outline" size={64} color="#C7C7CC" />
      <Text style={styles.emptyTitle}>No Matches Yet</Text>
      <Text style={styles.emptyDescription}>
        Add player matchups to start tracking individual results
      </Text>
    </View>
  );

  const renderPlayerSelector = (
    title: string,
    members: Member[] | undefined,
    selectedPlayerId: Id<'members'> | '',
    onSelect: (playerId: Id<'members'>) => void,
    onClose: () => void
  ) => (
    <View style={styles.bottomSheetContainer}>
      <View style={styles.bottomSheetHeader}>
        <Text style={styles.bottomSheetTitle}>{title}</Text>
        <TouchableOpacity onPress={onClose} style={styles.bottomSheetCloseButton}>
          <Ionicons name="close" size={24} color="#8E8E93" />
        </TouchableOpacity>
      </View>
      <FlatList
        data={members || []}
        keyExtractor={(item) => item._id}
        renderItem={({ item: member }) => (
          <TouchableOpacity
            style={[
              styles.playerSelectorItem,
              selectedPlayerId === member._id && styles.selectedPlayerItem
            ]}
            onPress={() => {
              if (Platform.OS !== 'web') {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              }
              onSelect(member._id);
              onClose();
            }}
          >
            <View style={styles.playerSelectorInfo}>
              <Text style={[
                styles.playerSelectorName,
                selectedPlayerId === member._id && styles.selectedPlayerName
              ]}>
                {member.name}
              </Text>
              {member.email && (
                <Text style={styles.playerSelectorEmail}>{member.email}</Text>
              )}
            </View>
            {selectedPlayerId === member._id && (
              <Ionicons name="checkmark-circle" size={20} color="#007AFF" />
            )}
          </TouchableOpacity>
        )}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );

  if (game === undefined || homeMembers === undefined || awayMembers === undefined) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading game...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!game) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Game not found</Text>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Text style={styles.backButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => {
            if (Platform.OS !== 'web') {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }
            router.back();
          }}
        >
          <Ionicons name="chevron-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.gameTitle}>{game.homeTeam} vs {game.awayTeam}</Text>
          <Text style={styles.gameDate}>{formatDate(game.date)}</Text>
          {game.location && (
            <Text style={styles.gameLocation}>{game.location}</Text>
          )}
        </View>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => {
            if (homeMembers.length === 0 || awayMembers.length === 0) {
              Alert.alert('No Players', 'Both teams need members to create matches');
              return;
            }
            if (Platform.OS !== 'web') {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            }
            setShowAddMatchModal(true);
          }}
        >
          <Ionicons name="add" size={24} color="#007AFF" />
        </TouchableOpacity>
      </View>

      {game.isCompleted && (
        <View style={styles.scoreHeader}>
          <View style={styles.finalScore}>
            <Text style={styles.finalScoreLabel}>Final Score</Text>
            <Text style={styles.finalScoreText}>
              {game.homeScore} - {game.awayScore}
            </Text>
          </View>
        </View>
      )}

      <FlatList
        data={game.matches}
        renderItem={renderMatchCard}
        keyExtractor={(item) => item._id}
        contentContainerStyle={[
          styles.listContainer,
          game.matches.length === 0 && styles.emptyListContainer,
        ]}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />

      {/* Add Match Modal */}
      <Modal
        visible={showAddMatchModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => {
                setShowAddMatchModal(false);
                setHomePlayerId('');
                setAwayPlayerId('');
              }}
            >
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>New Match</Text>
            <TouchableOpacity
              onPress={handleAddMatch}
              disabled={isCreating || !homePlayerId || !awayPlayerId}
            >
              <Text
                style={[
                  styles.createButton,
                  (!homePlayerId || !awayPlayerId || isCreating) && styles.disabledButton,
                ]}
              >
                {isCreating ? 'Creating...' : 'Create'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Home Player ({game.homeTeam})</Text>
              <TouchableOpacity
                style={styles.playerSelectorButton}
                onPress={() => setShowHomePlayerSheet(true)}
              >
                <Text style={[
                  styles.playerSelectorButtonText,
                  !homePlayerId && styles.placeholderText
                ]}>
                  {getPlayerName(homePlayerId, true)}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Away Player ({game.awayTeam})</Text>
              <TouchableOpacity
                style={styles.playerSelectorButton}
                onPress={() => setShowAwayPlayerSheet(true)}
              >
                <Text style={[
                  styles.playerSelectorButtonText,
                  !awayPlayerId && styles.placeholderText
                ]}>
                  {getPlayerName(awayPlayerId, false)}
                </Text>
                <Ionicons name="chevron-down" size={20} color="#8E8E93" />
              </TouchableOpacity>
            </View>
          </View>
        </SafeAreaView>
      </Modal>

      {/* Home Player Selection Bottom Sheet */}
      <Modal
        visible={showHomePlayerSheet}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.bottomSheetModal}>
          {renderPlayerSelector(
            `Select Home Player (${game.homeTeam})`,
            homeMembers,
            homePlayerId,
            setHomePlayerId,
            () => setShowHomePlayerSheet(false)
          )}
        </SafeAreaView>
      </Modal>

      {/* Away Player Selection Bottom Sheet */}
      <Modal
        visible={showAwayPlayerSheet}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.bottomSheetModal}>
          {renderPlayerSelector(
            `Select Away Player (${game.awayTeam})`,
            awayMembers,
            awayPlayerId,
            setAwayPlayerId,
            () => setShowAwayPlayerSheet(false)
          )}
        </SafeAreaView>
      </Modal>

      {/* Result Modal */}
      <Modal
        visible={showResultModal}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              onPress={() => {
                setShowResultModal(false);
                setSelectedMatch(null);
                resetResultForm();
              }}
            >
              <Text style={styles.cancelButton}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {selectedMatch ? `Match ${selectedMatch.matchNumber} Result` : 'Match Result'}
            </Text>
            <TouchableOpacity
              onPress={handleUpdateResult}
              disabled={!homeRacks || !awayRacks}
            >
              <Text
                style={[
                  styles.createButton,
                  (!homeRacks || !awayRacks) && styles.disabledButton,
                ]}
              >
                Save
              </Text>
            </TouchableOpacity>
          </View>

          {selectedMatch && (
            <ScrollView style={styles.modalContent}>
              <View style={styles.matchupPreview}>
                <Text style={styles.matchupText}>
                  {selectedMatch.homePlayer} vs {selectedMatch.awayPlayer}
                </Text>
              </View>

              {/* Racks Won Section */}
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Match Result</Text>
              </View>

              <View style={styles.inputRow}>
                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.homePlayer} Racks
                  </Text>
                  <View style={styles.numberInputContainer}>
                    <TouchableOpacity
                      style={styles.numberButton}
                      onPress={() => {
                        const current = parseInt(homeRacks) || 0;
                        if (current > 0) setHomeRacks((current - 1).toString());
                      }}
                    >
                      <Ionicons name="remove" size={20} color="#007AFF" />
                    </TouchableOpacity>
                    <Text style={styles.numberDisplay}>{homeRacks || '0'}</Text>
                    <TouchableOpacity
                      style={styles.numberButton}
                      onPress={() => {
                        const current = parseInt(homeRacks) || 0;
                        setHomeRacks((current + 1).toString());
                      }}
                    >
                      <Ionicons name="add" size={20} color="#007AFF" />
                    </TouchableOpacity>
                  </View>
                </View>

                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.awayPlayer} Racks
                  </Text>
                  <View style={styles.numberInputContainer}>
                    <TouchableOpacity
                      style={styles.numberButton}
                      onPress={() => {
                        const current = parseInt(awayRacks) || 0;
                        if (current > 0) setAwayRacks((current - 1).toString());
                      }}
                    >
                      <Ionicons name="remove" size={20} color="#007AFF" />
                    </TouchableOpacity>
                    <Text style={styles.numberDisplay}>{awayRacks || '0'}</Text>
                    <TouchableOpacity
                      style={styles.numberButton}
                      onPress={() => {
                        const current = parseInt(awayRacks) || 0;
                        setAwayRacks((current + 1).toString());
                      }}
                    >
                      <Ionicons name="add" size={20} color="#007AFF" />
                    </TouchableOpacity>
                  </View>
                </View>
              </View>

              {/* Player Points Section */}
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Player Points</Text>
              </View>

              <View style={styles.inputRow}>
                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.homePlayer} Points
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={homePlayerPoints}
                    onChangeText={setHomePlayerPoints}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.awayPlayer} Points
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={awayPlayerPoints}
                    onChangeText={setAwayPlayerPoints}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              {/* Break & Runs Section */}
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Break & Runs</Text>
              </View>

              <View style={styles.inputRow}>
                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.homePlayer} B&R
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={homeBreakAndRuns}
                    onChangeText={setHomeBreakAndRuns}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.awayPlayer} B&R
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={awayBreakAndRuns}
                    onChangeText={setAwayBreakAndRuns}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              {/* Table Runs Section */}
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Table Runs</Text>
              </View>

              <View style={styles.inputRow}>
                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.homePlayer} TR
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={homeTableRuns}
                    onChangeText={setHomeTableRuns}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    {selectedMatch.awayPlayer} TR
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={awayTableRuns}
                    onChangeText={setAwayTableRuns}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>

              {/* Opponent Run Outs Section */}
              <View style={styles.sectionHeader}>
                <Text style={styles.sectionTitle}>Opponent Run Outs (BR/TR)</Text>
              </View>

              <View style={styles.inputRow}>
                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    Against {selectedMatch.homePlayer}
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={homeOppRunOuts}
                    onChangeText={setHomeOppRunOuts}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>

                <View style={styles.inputColumn}>
                  <Text style={styles.inputLabel}>
                    Against {selectedMatch.awayPlayer}
                  </Text>
                  <TextInput
                    style={styles.numberInput}
                    value={awayOppRunOuts}
                    onChangeText={setAwayOppRunOuts}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </View>
              </View>
            </ScrollView>
          )}
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#8E8E93',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  errorText: {
    fontSize: 18,
    color: '#8E8E93',
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  backButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 17,
    color: '#007AFF',
    fontWeight: '600',
  },
  headerInfo: {
    flex: 1,
  },
  gameTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  gameDate: {
    fontSize: 14,
    color: '#8E8E93',
    marginTop: 2,
  },
  gameLocation: {
    fontSize: 14,
    color: '#007AFF',
    marginTop: 2,
  },
  addButton: {
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scoreHeader: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  finalScore: {
    alignItems: 'center',
  },
  finalScoreLabel: {
    fontSize: 14,
    color: '#8E8E93',
    fontWeight: '500',
    marginBottom: 4,
  },
  finalScoreText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#34C759',
  },
  listContainer: {
    padding: 20,
  },
  emptyListContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  matchCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 12,
    shadowColor: '#000000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  matchNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
  },
  statusBadge: {
    borderRadius: 8,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  completedBadge: {
    backgroundColor: '#E8F5E8',
  },
  completedText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#34C759',
  },
  matchup: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  playerContainer: {
    flex: 1,
    alignItems: 'center',
  },
  awayPlayer: {
    alignItems: 'center',
  },
  playerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 4,
  },
  homeLabel: {
    fontSize: 10,
    fontWeight: '600',
    color: '#34C759',
    letterSpacing: 0.5,
  },
  awayLabel: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FF9500',
    letterSpacing: 0.5,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 20,
  },
  score: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#8E8E93',
  },
  winningScore: {
    color: '#34C759',
  },
  scoreSeparator: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#8E8E93',
    marginHorizontal: 8,
  },
  vsText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#8E8E93',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#000000',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyDescription: {
    fontSize: 16,
    color: '#8E8E93',
    textAlign: 'center',
    lineHeight: 22,
    paddingHorizontal: 40,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  cancelButton: {
    fontSize: 17,
    color: '#007AFF',
  },
  modalTitle: {
    fontSize: 17,
    fontWeight: '600',
    color: '#000000',
  },
  createButton: {
    fontSize: 17,
    fontWeight: '600',
    color: '#007AFF',
  },
  disabledButton: {
    color: '#C7C7CC',
  },
  modalContent: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  matchupPreview: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    alignItems: 'center',
  },
  matchupText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  numberInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    paddingVertical: 12,
  },
  numberButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  numberDisplay: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginHorizontal: 32,
    minWidth: 40,
    textAlign: 'center',
  },
  sectionHeader: {
    marginTop: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    textAlign: 'center',
  },
  inputRow: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  inputColumn: {
    flex: 1,
  },
  numberInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#E5E5EA',
    textAlign: 'center',
  },
  playerSelectorButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#E5E5EA',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  playerSelectorButtonText: {
    fontSize: 16,
    color: '#000000',
  },
  placeholderText: {
    color: '#8E8E93',
  },
  bottomSheetModal: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  bottomSheetContainer: {
    flex: 1,
    backgroundColor: '#F2F2F7',
  },
  bottomSheetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E5EA',
  },
  bottomSheetTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
  },
  bottomSheetCloseButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F2F2F7',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playerSelectorItem: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F2F2F7',
    flexDirection: 'row',
    alignItems: 'center',
  },
  selectedPlayerItem: {
    backgroundColor: '#E3F2FD',
  },
  playerSelectorInfo: {
    flex: 1,
  },
  playerSelectorName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  selectedPlayerName: {
    color: '#007AFF',
  },
  playerSelectorEmail: {
    fontSize: 14,
    color: '#8E8E93',
  },
});