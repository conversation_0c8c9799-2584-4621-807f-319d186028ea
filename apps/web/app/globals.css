@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";
@import "@repo/tailwind-config/theme.css";
@import "@repo/tailwind-config/tailwind-animate.css";

@source "../node_modules/fumadocs-ui/dist/**/*.js";

@variant dark (&:where(.dark, .dark *));

pre.shiki {
	@apply mb-4 rounded-lg p-6;
}

#nd-sidebar {
	@apply bg-card! top-[4.5rem] md:h-[calc(100dvh-4.5rem)]!;

	button[data-search-full] {
		@apply bg-transparent;
	}
}

#nd-page .prose {
	h1,
	h2,
	h3,
	h4,
	h5,
	h6 {
		a {
			@apply no-underline!;
		}
	}
}

div[role="tablist"].bg-secondary {
	@apply bg-muted!;
}

input[cmdk-input] {
	@apply border-none focus-visible:ring-0;
}

:root {
  --background: oklch(1.0000 0 0);
  --foreground: oklch(0.1363 0.0364 259.2010);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0.1363 0.0364 259.2010);
  --popover: oklch(1.0000 0 0);
  --popover-foreground: oklch(0.1363 0.0364 259.2010);
  --primary: oklch(0.7302 0.1453 230.1851);
  --primary-foreground: oklch(0.9851 0 0);
  --secondary: oklch(0.9683 0.0069 247.8956);
  --secondary-foreground: oklch(0.2077 0.0398 265.7549);
  --muted: oklch(0.9683 0.0069 247.8956);
  --muted-foreground: oklch(0.5544 0.0407 257.4166);
  --accent: oklch(0.9514 0.0250 236.8242);
  --accent-foreground: oklch(0.4434 0.1000 240.7897);
  --destructive: oklch(0.6368 0.2078 25.3313);
  --destructive-foreground: oklch(0.9851 0 0);
  --border: oklch(0.9288 0.0126 255.5078);
  --input: oklch(0.9288 0.0126 255.5078);
  --ring: oklch(0.7302 0.1453 230.1851);
  --chart-1: oklch(0.7302 0.1453 230.1851);
  --chart-2: oklch(0.5106 0.2301 276.9656);
  --chart-3: oklch(0.6806 0.1423 75.8340);
  --chart-4: oklch(0.5771 0.2152 27.3250);
  --chart-5: oklch(0.6668 0.2591 322.1499);
  --sidebar: oklch(0.2077 0.0398 265.7549);
  --sidebar-foreground: oklch(0.7107 0.0351 256.7878);
  --sidebar-primary: oklch(0.9842 0.0034 247.8575);
  --sidebar-primary-foreground: oklch(0.2077 0.0398 265.7549);
  --sidebar-accent: oklch(0.7302 0.1453 230.1851);
  --sidebar-accent-foreground: oklch(0.9842 0.0034 247.8575);
  --sidebar-border: oklch(0.2795 0.0368 260.0310);
  --sidebar-ring: oklch(0.7302 0.1453 230.1851);
  --font-sans: Roboto, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.03);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.03);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 2px 4px -1px hsl(0 0% 0% / 0.05);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 4px 6px -1px hsl(0 0% 0% / 0.05);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 8px 10px -1px hsl(0 0% 0% / 0.05);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.13);
  --tracking-normal: 0rem;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.1448 0 0);
  --foreground: oklch(0.9851 0 0);
  --card: oklch(0.2103 0.0059 285.8852);
  --card-foreground: oklch(0.9851 0 0);
  --popover: oklch(0.2103 0.0059 285.8852);
  --popover-foreground: oklch(0.9851 0 0);
  --primary: oklch(0.7302 0.1453 230.1851);
  --primary-foreground: oklch(0.1363 0.0364 259.2010);
  --secondary: oklch(0.2739 0.0055 286.0326);
  --secondary-foreground: oklch(0.9851 0 0);
  --muted: oklch(0.2739 0.0055 286.0326);
  --muted-foreground: oklch(0.7118 0.0129 286.0665);
  --accent: oklch(0.7302 0.1453 230.1851);
  --accent-foreground: oklch(0.9851 0 0);
  --destructive: oklch(0.3958 0.1331 25.7230);
  --destructive-foreground: oklch(0.9851 0 0);
  --border: oklch(0.2739 0.0055 286.0326);
  --input: oklch(0.2739 0.0055 286.0326);
  --ring: oklch(0.7302 0.1453 230.1851);
  --chart-1: oklch(0.7302 0.1453 230.1851);
  --chart-2: oklch(0.5106 0.2301 276.9656);
  --chart-3: oklch(0.6806 0.1423 75.8340);
  --chart-4: oklch(0.5771 0.2152 27.3250);
  --chart-5: oklch(0.6668 0.2591 322.1499);
  --sidebar: oklch(0.2103 0.0059 285.8852);
  --sidebar-foreground: oklch(0.7118 0.0129 286.0665);
  --sidebar-primary: oklch(0.9851 0 0);
  --sidebar-primary-foreground: oklch(0.2103 0.0059 285.8852);
  --sidebar-accent: oklch(0.7302 0.1453 230.1851);
  --sidebar-accent-foreground: oklch(0.9851 0 0);
  --sidebar-border: oklch(0.2739 0.0055 286.0326);
  --sidebar-ring: oklch(0.7302 0.1453 230.1851);
  --font-sans: Roboto, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Roboto Mono, monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}